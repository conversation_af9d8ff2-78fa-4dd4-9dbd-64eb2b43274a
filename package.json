{"name": "shadcn-landing-page", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@devnomic/marquee": "^1.0.3", "@hookform/resolvers": "5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.518.0", "next": "15.3.4", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.58.1", "tailwind-merge": "3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/node": "24.0.3", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "eslint": "9.29.0", "eslint-config-next": "15.3.4", "postcss": "8.5.6", "tailwindcss": "^4.1.10", "typescript": "5.8.3"}}